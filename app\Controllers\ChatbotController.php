<?php

namespace App\Controllers;

use CodeIgniter\Controller;
use CodeIgniter\HTTP\ResponseInterface;

class ChatbotController extends Controller
{
    public function test()
    {
        try {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Chatbot API is working!',
                'timestamp' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Test failed: ' . $e->getMessage()
            ])->setStatusCode(500);
        }
    }

    public function chat()
    {
        try {
            // Only allow POST requests
            if ($this->request->getMethod() !== 'POST') {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Only POST requests allowed'
                ])->setStatusCode(405);
            }

        // Get the user message from POST data
        $input = $this->request->getJSON(true);
        $userMessage = $input['message'] ?? '';
        $conversationHistory = $input['history'] ?? [];

        if (empty($userMessage)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Message is required'
            ])->setStatusCode(400);
        }

        // Validate message length
        if (strlen($userMessage) > 500) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Message too long. Maximum 500 characters allowed.'
            ])->setStatusCode(400);
        }

        try {
            // Load DERS context from markdown file
            $contextFile = ROOTPATH . 'db_backups/applicants_info.md';
            $contextContent = '';

            if (file_exists($contextFile)) {
                $contextContent = file_get_contents($contextFile);
                // Convert markdown to plain text for AI context
                $contextContent = strip_tags($contextContent);
            } else {
                $contextContent = 'DERS (Dakoii Echad Recruitment & Selection System) is Papua New Guinea\'s first AI-integrated recruitment and selection system. For support, contact <EMAIL>';
            }

            // Get AI response
            $aiResponse = $this->getGeminiResponse($userMessage, $conversationHistory, $contextContent);

            return $this->response->setJSON([
                'success' => true,
                'response' => $aiResponse
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Chatbot error: ' . $e->getMessage());

            return $this->response->setJSON([
                'success' => false,
                'message' => 'Sorry, I encountered an error. Please try again or contact <NAME_EMAIL>'
            ])->setStatusCode(500);
        }
    }

    private function getGeminiResponse($userMessage, $conversationHistory, $contextContent)
    {
        // Get API configuration from environment
        $apiKey = env('GEMINI_API_KEY');
        $apiUrl = env('GEMINI_API_URL');

        if (empty($apiKey) || empty($apiUrl)) {
            throw new \Exception('Gemini API configuration not found');
        }

        // Prepare conversation history string
        $historyString = '';
        if (!empty($conversationHistory)) {
            $recentHistory = array_slice($conversationHistory, -6); // Last 6 messages
            foreach ($recentHistory as $msg) {
                $historyString .= $msg['role'] . ': ' . $msg['content'] . "\n";
            }
        }

        // Prepare the prompt with context
        $prompt = "You are a DERS (Dakoii Echad Recruitment & Selection System) assistant. You must ALWAYS consult and reference the following DERS information first before responding to any user question:

{$contextContent}

Previous conversation:
{$historyString}

User question: {$userMessage}

CRITICAL INSTRUCTIONS:
1. ALWAYS check the DERS information above first for relevant details
2. Base your response strictly on the provided DERS context
3. Provide precise, accurate answers using only the information from the DERS system guide
4. If the question is not covered in the DERS information, clearly state this and suggest contacting <NAME_EMAIL>
5. Keep responses concise but complete
6. Do not speculate or provide information not found in the DERS context
7. If the question is not related to DERS, politely redirect to DERS-related topics
8. ABSOLUTELY NEVER mention ANY specific AI model names (Gemini, GPT, Claude, ChatGPT, OpenAI, etc.) - ALWAYS refer to AI technology ONLY as \"Powerful Advanced AI\"
9. If asked specifically about what AI model or technology DERS uses, respond: \"DERS uses Powerful Advanced AI for document processing and analysis.\"
10. FORMAT ALL RESPONSES IN HTML - Use proper HTML tags like <strong>, <em>, <ul>, <li>, <br>, <p> etc. for formatting instead of markdown. This ensures proper display in the chat interface.

Respond based strictly on the DERS information provided above. Remember: NO SPECIFIC AI MODEL NAMES EVER! Use HTML formatting for all responses.";

        // Prepare request body
        $requestBody = [
            'contents' => [[
                'parts' => [[
                    'text' => $prompt
                ]]
            ]],
            'generationConfig' => [
                'temperature' => 0.1,
                'topK' => 40,
                'topP' => 0.95,
                'maxOutputTokens' => 1024,
            ]
        ];

        // Make API request
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $apiUrl . '?key=' . $apiKey);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($requestBody));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            throw new \Exception('cURL error: ' . $error);
        }

        if ($httpCode !== 200) {
            throw new \Exception('API request failed with status: ' . $httpCode);
        }

        $data = json_decode($response, true);
        
        if (!$data || !isset($data['candidates'][0]['content']['parts'][0]['text'])) {
            throw new \Exception('Invalid API response format');
        }

        return $data['candidates'][0]['content']['parts'][0]['text'];
    }
}
